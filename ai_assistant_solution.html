<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手解决方案</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-shadow {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .icon-circle {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
        }
        .problem-card {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        }
        .support-card {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
        }
    </style>
</head>
<body class="bg-gray-50 p-4">
    <div class="max-w-7xl mx-auto bg-white rounded-2xl card-shadow overflow-hidden" style="aspect-ratio: 16/9;">
        <!-- Header -->
        <div class="gradient-bg text-white p-6">
            <h1 class="text-3xl font-bold text-center">AI助手解决方案</h1>
            <p class="text-center text-blue-100 mt-2">提升门店运营效率，赋能一线业务</p>
        </div>

        <div class="p-6 h-full">
            <div class="grid grid-cols-3 gap-6 h-full">
                <!-- 解决什么问题 -->
                <div class="space-y-4">
                    <div class="flex items-center mb-4">
                        <div class="icon-circle w-8 h-8 rounded-full flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>
                        <h2 class="text-xl font-bold text-gray-800">解决什么问题</h2>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                            <h3 class="font-semibold text-blue-800 mb-2">1. 售前赋能</h3>
                            <p class="text-sm text-blue-700">商品智能推荐、竞品对比、商品问答、一页纸查询、套购等，提升成交率</p>
                        </div>
                        
                        <div class="bg-green-50 p-4 rounded-lg border-l-4 border-green-500">
                            <h3 class="font-semibold text-green-800 mb-2">2. 导购培训</h3>
                            <p class="text-sm text-green-700">利用商品问答、一页纸、讲解视频等学习商品知识；开发每日优势文案晨读、AI导购培训助手</p>
                        </div>
                        
                        <div class="bg-purple-50 p-4 rounded-lg border-l-4 border-purple-500">
                            <h3 class="font-semibold text-purple-800 mb-2">3. 营销赋能</h3>
                            <p class="text-sm text-purple-700">提供专业门店海报、契合运中双周活动，降低营销内容制作门槛</p>
                        </div>
                    </div>
                </div>

                <!-- 目前试点问题 -->
                <div class="space-y-4">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 rounded-full bg-red-500 flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <h2 class="text-xl font-bold text-gray-800">目前试点问题</h2>
                    </div>
                    
                    <div class="space-y-2">
                        <div class="problem-card p-3 rounded-lg">
                            <div class="flex items-start">
                                <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full mr-2 mt-0.5">1</span>
                                <p class="text-sm text-gray-700">普及差、未推广，大部分店长不知道或看到入口也没用过</p>
                            </div>
                        </div>
                        
                        <div class="problem-card p-3 rounded-lg">
                            <div class="flex items-start">
                                <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full mr-2 mt-0.5">2</span>
                                <p class="text-sm text-gray-700">知识库待完善，如废水比、几排管、留空间隙等常见问题知识库不具备</p>
                            </div>
                        </div>
                        
                        <div class="problem-card p-3 rounded-lg">
                            <div class="flex items-start">
                                <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full mr-2 mt-0.5">3</span>
                                <p class="text-sm text-gray-700">各个品类知识没有明确的负责人，知识闭环效率低</p>
                            </div>
                        </div>
                        
                        <div class="problem-card p-3 rounded-lg">
                            <div class="flex items-start">
                                <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full mr-2 mt-0.5">4</span>
                                <p class="text-sm text-gray-700">竞品功能未能开放，数据质量验证等事项推进较慢</p>
                            </div>
                        </div>
                        
                        <div class="problem-card p-3 rounded-lg">
                            <div class="flex items-start">
                                <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full mr-2 mt-0.5">5</span>
                                <p class="text-sm text-gray-700">简称问题，大量一线使用的简称没有在线化，无法跟知识对应</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 需要哪些支持 -->
                <div class="space-y-4">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <h2 class="text-xl font-bold text-gray-800">需要哪些支持</h2>
                    </div>
                    
                    <div class="space-y-2">
                        <div class="support-card p-3 rounded-lg">
                            <div class="flex items-start">
                                <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full mr-2 mt-0.5">1</span>
                                <p class="text-sm text-gray-700">跟负责培训的部门联动，确保培训侧老师的一页纸及时进入知识库</p>
                            </div>
                        </div>
                        
                        <div class="support-card p-3 rounded-lg">
                            <div class="flex items-start">
                                <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full mr-2 mt-0.5">2</span>
                                <p class="text-sm text-gray-700">明确营销简称的责任人，录入系统的节点、字段</p>
                            </div>
                        </div>
                        
                        <div class="support-card p-3 rounded-lg">
                            <div class="flex items-start">
                                <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full mr-2 mt-0.5">3</span>
                                <p class="text-sm text-gray-700">明确品类知识的责任人，商品知识入库、纠错、运营通畅</p>
                            </div>
                        </div>
                        
                        <div class="support-card p-3 rounded-lg">
                            <div class="flex items-start">
                                <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full mr-2 mt-0.5">4</span>
                                <p class="text-sm text-gray-700">主配件关系维护的责任人，录入系统的节点、字段</p>
                            </div>
                        </div>
                        
                        <div class="support-card p-3 rounded-lg">
                            <div class="flex items-start">
                                <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full mr-2 mt-0.5">5</span>
                                <p class="text-sm text-gray-700">明确AI助手相关业务组织</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
