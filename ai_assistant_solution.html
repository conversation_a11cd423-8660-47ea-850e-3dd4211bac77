<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手解决方案</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
        }
        .header-bg {
            background: #7dd3fc;
        }
        .card-shadow {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }
        .icon-circle {
            background: #60a5fa;
        }
        .solution-card-1 {
            background: #fef3c7;
        }
        .solution-card-2 {
            background: #d1fae5;
        }
        .solution-card-3 {
            background: #e0e7ff;
        }
        .problem-card {
            background: #fed7d7;
        }
        .support-card {
            background: #d1fae5;
        }
        .number-badge {
            background: #6b7280;
        }
        .problem-badge {
            background: #ef4444;
        }
        .support-badge {
            background: #10b981;
        }
    </style>
</head>
<body class="bg-gray-50 p-4">
    <div class="max-w-7xl mx-auto bg-white rounded-lg card-shadow overflow-hidden" style="aspect-ratio: 16/9;">
        <!-- Header -->
        <div class="header-bg text-white p-6">
            <h1 class="text-3xl font-bold text-center text-gray-800">AI助手解决方案</h1>
            <p class="text-center text-gray-600 mt-2">提升门店运营效率，赋能一线业务</p>
        </div>

        <div class="p-6 h-full">
            <div class="grid grid-cols-3 gap-6 h-full">
                <!-- 解决什么问题 -->
                <div class="space-y-4">
                    <div class="flex items-center mb-4">
                        <div class="icon-circle w-8 h-8 rounded flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-700">解决什么问题</h2>
                    </div>

                    <div class="space-y-3">
                        <div class="solution-card-1 p-4 rounded">
                            <h3 class="font-medium text-gray-700 mb-2">1. 售前赋能</h3>
                            <p class="text-sm text-gray-600">商品智能推荐、竞品对比、商品问答、一页纸查询、套购等，提升成交率</p>
                        </div>

                        <div class="solution-card-2 p-4 rounded">
                            <h3 class="font-medium text-gray-700 mb-2">2. 导购培训</h3>
                            <p class="text-sm text-gray-600">利用商品问答、一页纸、讲解视频等学习商品知识；开发每日优势文案晨读、AI导购培训助手</p>
                        </div>

                        <div class="solution-card-3 p-4 rounded">
                            <h3 class="font-medium text-gray-700 mb-2">3. 营销赋能</h3>
                            <p class="text-sm text-gray-600">提供专业门店海报、契合运中双周活动，降低营销内容制作门槛</p>
                        </div>
                    </div>
                </div>

                <!-- 目前试点问题 -->
                <div class="space-y-4">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 rounded bg-red-400 flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-700">目前试点问题</h2>
                    </div>

                    <div class="space-y-2">
                        <div class="problem-card p-3 rounded">
                            <div class="flex items-start">
                                <span class="problem-badge text-white text-xs px-2 py-1 rounded mr-2 mt-0.5">1</span>
                                <p class="text-sm text-gray-600">普及差、未推广，大部分店长不知道或看到入口也没用过</p>
                            </div>
                        </div>

                        <div class="problem-card p-3 rounded">
                            <div class="flex items-start">
                                <span class="problem-badge text-white text-xs px-2 py-1 rounded mr-2 mt-0.5">2</span>
                                <p class="text-sm text-gray-600">知识库待完善，如废水比、几排管、留空间隙等常见问题知识库不具备</p>
                            </div>
                        </div>

                        <div class="problem-card p-3 rounded">
                            <div class="flex items-start">
                                <span class="problem-badge text-white text-xs px-2 py-1 rounded mr-2 mt-0.5">3</span>
                                <p class="text-sm text-gray-600">各个品类知识没有明确的负责人，知识闭环效率低</p>
                            </div>
                        </div>

                        <div class="problem-card p-3 rounded">
                            <div class="flex items-start">
                                <span class="problem-badge text-white text-xs px-2 py-1 rounded mr-2 mt-0.5">4</span>
                                <p class="text-sm text-gray-600">竞品功能未能开放，数据质量验证等事项推进较慢</p>
                            </div>
                        </div>

                        <div class="problem-card p-3 rounded">
                            <div class="flex items-start">
                                <span class="problem-badge text-white text-xs px-2 py-1 rounded mr-2 mt-0.5">5</span>
                                <p class="text-sm text-gray-600">简称问题，大量一线使用的简称没有在线化，无法跟知识对应</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 需要哪些支持 -->
                <div class="space-y-4">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 rounded bg-green-400 flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-700">需要哪些支持</h2>
                    </div>

                    <div class="space-y-2">
                        <div class="support-card p-3 rounded">
                            <div class="flex items-start">
                                <span class="support-badge text-white text-xs px-2 py-1 rounded mr-2 mt-0.5">1</span>
                                <p class="text-sm text-gray-600">跟负责培训的部门联动，确保培训侧老师的一页纸及时进入知识库</p>
                            </div>
                        </div>

                        <div class="support-card p-3 rounded">
                            <div class="flex items-start">
                                <span class="support-badge text-white text-xs px-2 py-1 rounded mr-2 mt-0.5">2</span>
                                <p class="text-sm text-gray-600">明确营销简称的责任人，录入系统的节点、字段</p>
                            </div>
                        </div>

                        <div class="support-card p-3 rounded">
                            <div class="flex items-start">
                                <span class="support-badge text-white text-xs px-2 py-1 rounded mr-2 mt-0.5">3</span>
                                <p class="text-sm text-gray-600">明确品类知识的责任人，商品知识入库、纠错、运营通畅</p>
                            </div>
                        </div>

                        <div class="support-card p-3 rounded">
                            <div class="flex items-start">
                                <span class="support-badge text-white text-xs px-2 py-1 rounded mr-2 mt-0.5">4</span>
                                <p class="text-sm text-gray-600">主配件关系维护的责任人，录入系统的节点、字段</p>
                            </div>
                        </div>

                        <div class="support-card p-3 rounded">
                            <div class="flex items-start">
                                <span class="support-badge text-white text-xs px-2 py-1 rounded mr-2 mt-0.5">5</span>
                                <p class="text-sm text-gray-600">明确AI助手相关业务组织</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
